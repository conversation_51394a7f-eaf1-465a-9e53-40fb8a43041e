import { useState } from "react";
import { Keyboard } from "lucide-react";
import { Link } from "wouter";
import { languageOptions, difficultyOptions } from "@shared/schema";
import { TestConfiguration } from "@/components/test-configuration";
import { StatisticsPanel } from "@/components/statistics-panel";
import { TypingTest } from "@/components/typing-test";
import { ResultsPanel } from "@/components/results-panel";
import { VirtualKeyboard } from "@/components/virtual-keyboard";
import { useTypingTest } from "@/hooks/use-typing-test";
import { getTranslation } from "@/lib/i18n";

export default function Home() {
  const [language, setLanguage] = useState("en");
  const [difficulty, setDifficulty] = useState("intermediate");
  const [duration, setDuration] = useState(60);
  const [showKeyboard, setShowKeyboard] = useState(true);

  const {
    testState,
    isTestActive,
    isTestCompleted,
    startTest,
    resetTest,
    handleKeyPress,
    handleCompositionInput,
    progress
  } = useTypingTest(language, difficulty, duration);

  return (
    <div className="bg-slate-50 font-sans min-h-screen">
      {/* Header */}
      <header className="bg-white border-b border-slate-200 sticky top-0 z-50">
        <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-slate-800">
                <Keyboard className="inline-block w-6 h-6 text-blue-600 mr-2" />
                {getTranslation(language, 'appName')}
              </h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="relative">
                <select 
                  value={language}
                  onChange={(e) => setLanguage(e.target.value)}
                  disabled={isTestActive}
                  className="appearance-none bg-white border border-slate-300 rounded-lg px-4 py-2 pr-8 text-sm font-medium text-slate-700 hover:border-blue-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50"
                >
                  {languageOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              
              <div className="relative">
                <select 
                  value={difficulty}
                  onChange={(e) => setDifficulty(e.target.value)}
                  disabled={isTestActive}
                  className="appearance-none bg-white border border-slate-300 rounded-lg px-4 py-2 pr-8 text-sm font-medium text-slate-700 hover:border-blue-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50"
                >
                  {difficultyOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {getTranslation(language, option.labelKey)}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </nav>
      </header>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Test Configuration */}
        <TestConfiguration
          duration={duration}
          setDuration={setDuration}
          isTestActive={isTestActive}
          onStart={startTest}
          onReset={resetTest}
          language={language}
        />

        {/* Statistics Panel */}
        <StatisticsPanel
          wpm={testState.wpm}
          accuracy={testState.accuracy}
          timeRemaining={testState.timeRemaining}
          errors={testState.errors}
          language={language}
        />

        {/* Typing Test Area */}
        <TypingTest
          language={language}
          difficulty={difficulty}
          testState={testState}
          isTestActive={isTestActive}
          onKeyPress={handleKeyPress}
          onCompositionInput={handleCompositionInput}
          progress={progress}
        />

        {/* Results Panel */}
        {isTestCompleted && (
          <ResultsPanel
            testState={testState}
            language={language}
            difficulty={difficulty}
            duration={duration}
            onRetake={resetTest}
          />
        )}

        {/* Virtual Keyboard */}
        <VirtualKeyboard
          isVisible={showKeyboard}
          onToggleVisibility={() => setShowKeyboard(!showKeyboard)}
          currentChar={testState.testText?.[testState.currentPosition]}
          language={language}
          onKeyPress={handleKeyPress}
        />
      </main>

      <footer className="bg-white border-t border-slate-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-slate-600">
            <p>&copy; 2024 {getTranslation(language, 'appName')}. {getTranslation(language, 'footerText')}</p>
            <div className="mt-4 space-x-6">
              <Link href={`/privacy-policy?lang=${language}`} className="text-sm hover:text-blue-600">
                {getTranslation(language, 'privacyPolicy')}
              </Link>
              <Link href={`/terms-of-service?lang=${language}`} className="text-sm hover:text-blue-600">
                {getTranslation(language, 'termsOfService')}
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
